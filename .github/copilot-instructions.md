# GitHub Copilot Instructions for Shreds Decoder

## Project Overview

This is a high-performance Rust CLI application that serves as a proxy server for Solana blockchain transaction streaming. The application connects to ERPC's Direct Shreds service, decodes shreds into transactions, and streams them to clients via gRPC.

**Key Architecture:**

```
ERPC Direct Shreds → Shreds Decoder (Proxy) → gRPC Client Applications
```

## Project Structure Guidelines

### Directory Structure

```
shreds-decoder/
├── proto/                         # Protocol Buffers definitions
└── src/
    ├── main.rs                    # Entry point & CLI
    ├── config/                    # Configuration management (split into modular files)
    ├── client/                    # ERPC client connection
    ├── grpc/                      # gRPC server implementation
    ├── types/                     # Custom types & models
    └── utils/                     # Reusable utilities
```

### Module Responsibilities

-   **`proto/`**: gRPC Protocol Buffer definitions
-   **`config/`**: Central configuration management (all modules depend on this)
-   **`client/`**: ERPC connection
-   **`grpc/`**: Server implementation → serves clients
-   **`types/`**: Shared types used across all modules
-   **`utils/`**: Helper functions used across all modules

### Module Guidelines

-   Use shared types from `types/` module across all modules
-   Load configuration from `config/` module in all services
-   Leverage `utils/` for common functionality (retry, validation, metrics)
-   Maintain clean dependencies, avoid circular dependencies
-   Each module should have a clear, single responsibility

## Rust Guidelines

### Code Style and Formatting

-   Use `cargo fmt` for automatic code formatting
-   Follow Rust 2024 edition idioms and best practices
-   Use `snake_case` for variable and function names
-   Use `PascalCase` for types, structs, and enums
-   Use `SCREAMING_SNAKE_CASE` for constants
-   Prefer explicit return types for public functions
-   Use 4 spaces for indentation (configured in rustfmt.toml)

### Error Handling

-   Use `anyhow::Result<T>` for functions that can fail
-   Use `thiserror` for custom error types when domain-specific errors are needed
-   Prefer `?` operator for error propagation
-   Add context to errors using `.context()` from anyhow
-   Never use `unwrap()` or `expect()` in production code - use proper error handling

### Async Programming

-   Use `tokio` runtime for all async operations
-   Prefer `async/await` over manual Future implementations
-   Use `tokio::spawn` for concurrent tasks
-   Use channels for communication between async tasks
-   Handle cancellation gracefully with `tokio::select!` when needed

### Memory Management

-   Prefer `&str` over `String` for function parameters when possible
-   Use `Arc<T>` for shared ownership in concurrent contexts
-   Use `Mutex<T>` or `RwLock<T>` for shared mutable state
-   Avoid unnecessary allocations in hot paths
-   Use `Vec<T>` for dynamic arrays, prefer iterators over collecting

## Project-Specific Guidelines

### Solana Integration

-   Use official Solana SDK crates: `solana-entry`, `solana-transaction`, etc.
-   Always use the latest stable versions of Solana dependencies
-   Handle `VersionedTransaction` properly for both Legacy and V0 formats
-   Implement proper account filtering based on client subscriptions

### gRPC Implementation

-   Use `tonic` framework for gRPC server and client implementations
-   Define clear protobuf schemas in `.proto` files
-   Handle streaming responses appropriately with proper error handling
-   Implement graceful shutdown for streaming connections
-   **Performance-first**: Use plain HTTP/2 without TLS for maximum speed in trusted environments
-   **Connection pooling**: Maintain persistent gRPC connections to avoid handshake overhead
-   **Binary serialization**: Optimize protobuf messages for minimal size and fast serialization

### ERPC Direct Shreds Integration

-   Use `solana-stream-sdk` for connecting to ERPC services
-   Implement automatic reconnection logic with exponential backoff
-   Handle shred deduplication efficiently
-   Subscribe only to necessary accounts to optimize performance

### Configuration Management

-   Use `config` crate for loading configuration from files and environment
-   Support both TOML configuration files and environment variables
-   Validate configuration on startup

### Logging and Monitoring

-   Use `tracing` and `tracing-subscriber` for structured logging
-   Include relevant context in log messages (slot numbers, account addresses, etc.)
-   Use appropriate log levels: `error!`, `warn!`, `info!`, `debug!`, `trace!`
-   Log performance metrics for critical operations

## Architecture Patterns

### Connection Management

-   Implement connection pooling for ERPC connections
-   Use health checks to monitor connection status
-   Handle network failures gracefully with retry logic
-   Maintain connection state with proper cleanup

### Data Processing Pipeline

```
Raw Shreds → Shred Decoding → Entry Extraction → Transaction Filtering → gRPC Broadcasting
```

### Performance Optimization

-   Use efficient data structures for account filtering (HashSet, Bloom filters)
-   Minimize allocations in the hot path
-   Use zero-copy operations where possible
-   Implement proper buffering for streaming data

### Realtime Streaming Optimization

-   **Avoid TLS overhead**: Use plain HTTP/2 for gRPC connections to minimize encryption overhead
-   **TCP_NODELAY**: Enable TCP_NODELAY on all socket connections to reduce latency
-   **Connection reuse**: Maintain persistent connections, avoid frequent reconnections
-   **Minimal serialization**: Use binary formats, avoid JSON/text serialization in hot paths
-   **Lock-free data structures**: Prefer atomic operations and lock-free algorithms where possible
-   **CPU affinity**: Pin critical threads to specific CPU cores to avoid context switching
-   **Memory pools**: Pre-allocate buffers and reuse memory to avoid garbage collection pauses
-   **Batch processing**: Process multiple shreds/transactions in batches when possible
-   **Inline small functions**: Use `#[inline]` attribute for frequently called small functions
-   **Compile optimizations**: Use `lto = true` and `codegen-units = 1` in release profile

## Security Considerations

### Credential Management

-   Never hardcode API keys or credentials in source code
-   Use environment variables or secure configuration files
-   Validate all input from external sources (shreds, gRPC requests)
-   Implement proper authentication for gRPC endpoints if required

### Network Security

-   Validate and sanitize account addresses
-   Implement rate limiting to prevent abuse
-   Monitor for unusual patterns in incoming data
-   **Performance vs Security**: Prioritize speed over TLS encryption in trusted network environments
-   **Input validation**: Use fast validation methods that don't compromise latency

## Dependencies and Versions

### Core Dependencies

-   `tokio` - Async runtime (latest stable)
-   `anyhow` - Error handling
-   `config` - Configuration management
-   `tracing` - Logging
-   `tonic` - gRPC framework
-   `solana-stream-sdk` - ERPC integration
-   `solana-entry` - Solana blockchain primitives

### Development Dependencies

-   `cargo-make` - Build automation
-   `clippy` - Linting (strict configuration in clippy.toml)
-   `rustfmt` - Code formatting

## Development Policies

### Testing and Documentation

-   **No testing required**: This application does not require unit tests, integration tests, or any testing infrastructure
-   **No documentation required**: Do not generate rustdoc comments, README updates, or any form of documentation
-   **No comments in code**: Do not add any comments in source code, keep code clean and self-explanatory
-   **Focused implementation**: Create only exactly what is requested, avoid adding extra features or utilities

### Code Modification Guidelines

-   **Preserve existing logic**: When editing code, minimize changes to existing logic and functionality
-   **Confirmation required**: Any modifications that could alter existing behavior must be confirmed before implementation
-   **Surgical changes**: Make precise, targeted modifications rather than broad refactoring

## Code Quality Standards

### Clippy Configuration

-   Follow the strict clippy rules defined in clippy.toml
-   Cognitive complexity threshold: 30
-   Maximum function lines: 100
-   Handle all clippy warnings as errors in CI

### Performance Requirements

-   Target sub-millisecond latency for transaction streaming
-   Minimize memory allocation in critical paths
-   Handle thousands of concurrent gRPC connections
-   Efficient shred processing with minimal CPU usage

## Build and Deployment

### Cross-Platform Support

-   Support macOS (ARM64) and Linux (x86_64) targets
-   Use the provided Makefile for consistent builds
-   Create release binaries for distribution
