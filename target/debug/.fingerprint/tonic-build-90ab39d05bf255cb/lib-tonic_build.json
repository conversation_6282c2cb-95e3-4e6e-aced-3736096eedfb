{"rustc": 15497389221046826682, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 3882023571813807903, "profile": 3033921117576893, "path": 11967964535478927124, "deps": [[99783594999256520, "prost_build", false, 1598309580139612239], [3060637413840920116, "proc_macro2", false, 12622160946388413999], [8549471757621926118, "prettyplease", false, 446817699774864192], [17990358020177143287, "quote", false, 14745520146559958993], [18149961000318489080, "syn", false, 10715809610798474639]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tonic-build-90ab39d05bf255cb/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}