{"$message_type":"diagnostic","message":"unresolved import `proto::shreds_stream_client`","code":{"code":"E0432","explanation":"An import was unresolved.\n\nErroneous code example:\n\n```compile_fail,E0432\nuse something::Foo; // error: unresolved import `something::Foo`.\n```\n\nIn Rust 2015, paths in `use` statements are relative to the crate root. To\nimport items relative to the current and parent modules, use the `self::` and\n`super::` prefixes, respectively.\n\nIn Rust 2018 or later, paths in `use` statements are relative to the current\nmodule unless they begin with the name of a crate or a literal `crate::`, in\nwhich case they start from the crate root. As in Rust 2015 code, the `self::`\nand `super::` prefixes refer to the current and parent modules respectively.\n\nAlso verify that you didn't misspell the import name and that the import exists\nin the module from where you tried to import it. Example:\n\n```\nuse self::something::Foo; // Ok.\n\nmod something {\n    pub struct Foo;\n}\n# fn main() {}\n```\n\nIf you tried to use a module from an external crate and are using Rust 2015,\nyou may have missed the `extern crate` declaration (which is usually placed in\nthe crate root):\n\n```edition2015\nextern crate core; // Required to use the `core` crate in Rust 2015.\n\nuse core::any;\n# fn main() {}\n```\n\nSince Rust 2018 the `extern crate` declaration is not required and\nyou can instead just `use` it:\n\n```edition2018\nuse core::any; // No extern crate required in Rust 2018.\n# fn main() {}\n```\n"},"level":"error","spans":[{"file_name":"examples/simple_client.rs","byte_start":122,"byte_end":142,"line_start":8,"line_end":8,"column_start":12,"column_end":32,"is_primary":true,"text":[{"text":"use proto::shreds_stream_client::ShredsStreamClient;","highlight_start":12,"highlight_end":32}],"label":"could not find `shreds_stream_client` in `proto`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0432]\u001b[0m\u001b[0m\u001b[1m: unresolved import `proto::shreds_stream_client`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mexamples/simple_client.rs:8:12\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse proto::shreds_stream_client::ShredsStreamClient;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mcould not find `shreds_stream_client` in `proto`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `GetActiveSubscriptionsRequest` and `UpdateSubscriptionRequest`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"examples/simple_client.rs","byte_start":204,"byte_end":233,"line_start":9,"line_end":9,"column_start":41,"column_end":70,"is_primary":true,"text":[{"text":"use proto::{AccountSubscriptionRequest, GetActiveSubscriptionsRequest, UpdateSubscriptionRequest};","highlight_start":41,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"examples/simple_client.rs","byte_start":235,"byte_end":260,"line_start":9,"line_end":9,"column_start":72,"column_end":97,"is_primary":true,"text":[{"text":"use proto::{AccountSubscriptionRequest, GetActiveSubscriptionsRequest, UpdateSubscriptionRequest};","highlight_start":72,"highlight_end":97}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"examples/simple_client.rs","byte_start":202,"byte_end":260,"line_start":9,"line_end":9,"column_start":39,"column_end":97,"is_primary":true,"text":[{"text":"use proto::{AccountSubscriptionRequest, GetActiveSubscriptionsRequest, UpdateSubscriptionRequest};","highlight_start":39,"highlight_end":97}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"examples/simple_client.rs","byte_start":175,"byte_end":176,"line_start":9,"line_end":9,"column_start":12,"column_end":13,"is_primary":true,"text":[{"text":"use proto::{AccountSubscriptionRequest, GetActiveSubscriptionsRequest, UpdateSubscriptionRequest};","highlight_start":12,"highlight_end":13}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"examples/simple_client.rs","byte_start":260,"byte_end":261,"line_start":9,"line_end":9,"column_start":97,"column_end":98,"is_primary":true,"text":[{"text":"use proto::{AccountSubscriptionRequest, GetActiveSubscriptionsRequest, UpdateSubscriptionRequest};","highlight_start":97,"highlight_end":98}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `GetActiveSubscriptionsRequest` and `UpdateSubscriptionRequest`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0mexamples/simple_client.rs:9:41\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m9\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse proto::{AccountSubscriptionRequest, GetActiveSubscriptionsRequest, UpdateSubscriptionRequest};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error; 1 warning emitted","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m: aborting due to 1 previous error; 1 warning emitted\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0432`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1mFor more information about this error, try `rustc --explain E0432`.\u001b[0m\n"}
