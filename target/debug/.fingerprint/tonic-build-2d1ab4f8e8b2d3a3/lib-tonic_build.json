{"rustc": 15497389221046826682, "features": "[\"default\", \"prost\", \"prost-build\", \"transport\"]", "declared_features": "[\"cleanup-markdown\", \"default\", \"prost\", \"prost-build\", \"transport\"]", "target": 9025750215440372010, "profile": 16572146140859760712, "path": 7934114986830911633, "deps": [[2739579679802620019, "prost_build", false, 3616577105028981950], [3060637413840920116, "proc_macro2", false, 12622160946388413999], [8549471757621926118, "prettyplease", false, 446817699774864192], [16470553738848018267, "prost_types", false, 3760922967873686054], [17990358020177143287, "quote", false, 14745520146559958993], [18149961000318489080, "syn", false, 10715809610798474639]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tonic-build-2d1ab4f8e8b2d3a3/dep-lib-tonic_build", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}