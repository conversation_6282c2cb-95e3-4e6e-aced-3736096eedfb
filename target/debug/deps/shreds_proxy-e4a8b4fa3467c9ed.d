/Users/<USER>/Projects/shreds-decoder/target/debug/deps/libshreds_proxy-e4a8b4fa3467c9ed.rmeta: src/main.rs src/client/mod.rs src/client/erpc.rs src/config/mod.rs src/config/app.rs src/config/erpc.rs src/config/grpc.rs src/config/logging.rs src/config/performance.rs src/grpc/mod.rs src/grpc/broadcaster.rs src/grpc/server.rs src/grpc/service.rs src/types/mod.rs src/types/error.rs src/types/result.rs src/utils/mod.rs src/utils/logging.rs /Users/<USER>/Projects/shreds-decoder/target/debug/build/shreds-proxy-40b7833077296c81/out/shreds_stream.rs

/Users/<USER>/Projects/shreds-decoder/target/debug/deps/shreds_proxy-e4a8b4fa3467c9ed.d: src/main.rs src/client/mod.rs src/client/erpc.rs src/config/mod.rs src/config/app.rs src/config/erpc.rs src/config/grpc.rs src/config/logging.rs src/config/performance.rs src/grpc/mod.rs src/grpc/broadcaster.rs src/grpc/server.rs src/grpc/service.rs src/types/mod.rs src/types/error.rs src/types/result.rs src/utils/mod.rs src/utils/logging.rs /Users/<USER>/Projects/shreds-decoder/target/debug/build/shreds-proxy-40b7833077296c81/out/shreds_stream.rs

src/main.rs:
src/client/mod.rs:
src/client/erpc.rs:
src/config/mod.rs:
src/config/app.rs:
src/config/erpc.rs:
src/config/grpc.rs:
src/config/logging.rs:
src/config/performance.rs:
src/grpc/mod.rs:
src/grpc/broadcaster.rs:
src/grpc/server.rs:
src/grpc/service.rs:
src/types/mod.rs:
src/types/error.rs:
src/types/result.rs:
src/utils/mod.rs:
src/utils/logging.rs:
/Users/<USER>/Projects/shreds-decoder/target/debug/build/shreds-proxy-40b7833077296c81/out/shreds_stream.rs:

# env-dep:CARGO_PKG_NAME=shreds-proxy
# env-dep:CARGO_PKG_VERSION=0.1.0
# env-dep:OUT_DIR=/Users/<USER>/Projects/shreds-decoder/target/debug/build/shreds-proxy-40b7833077296c81/out
