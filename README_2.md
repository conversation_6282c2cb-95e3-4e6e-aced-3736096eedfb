# Shreds Decoder - Solana Transaction Streaming Proxy

[![Rust](https://img.shields.io/badge/rust-1.80+-orange.svg)](https://www.rust-lang.org)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)](https://github.com/dieppk/shreds-decoder)

A high-performance Rust CLI application that serves as a proxy server for Solana blockchain transaction streaming. This application connects to ERPC's Direct Shreds service, decodes shreds into transactions, and streams them to clients via gRPC.

## 📊 Project Status

**Current Phase**: Core Module Development
**Version**: 0.1.0
**Rust Edition**: 2024

**✅ Completed**: Optimal directory structure design and implementation
**🔄 In Progress**: Core module development
**📋 Next**: Main feature implementation

## 🎯 Overview

`shreds-decoder` acts as an intelligent middleware between ERPC's Direct Shreds service and client applications. It provides real-time Solana transaction streaming with account-based filtering through a simple gRPC interface.

### Key Features

-   **Real-time Transaction Streaming**: Live Solana transaction data via gRPC streams
-   **Account-based Filtering**: Subscribe to specific wallet addresses
-   **Automatic Reconnection**: Robust connection handling with auto-retry mechanisms
-   **High Performance**: Built with Rust for optimal speed and memory efficiency
-   **Simple Integration**: Clean gRPC API with protocol buffer definitions

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   ERPC Direct   │───▶│  Shreds Decoder  │───▶│   gRPC Client   │
│     Shreds      │    │     (Proxy)      │    │   Application   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
    Shreds Data            ┌──────────────┐              │
                          │   Decoder     │              │
                          │   Engine      │              │
                          └──────────────┘              │
                                 │                       │
                                 ▼                       │
                          VersionedTransaction ──────────┘
```

## 🏗️ Project Structure

```
shreds-decoder/
├── proto/                         # Protocol Buffers definitions
└── src/
    ├── main.rs                    # Entry point & CLI
    ├── config/                    # Configuration management (split into modular config files)
    ├── client/                    # ERPC client connection
    ├── grpc/                      # gRPC server implementation
    ├── types/                     # Custom types & models
    └── utils/                     # Reusable utilities
```

### Module Dependencies

-   **`proto/`**: gRPC Protocol Buffer definitions
-   **`config/`**: Central configuration management (all modules depend on this)
-   **`client/`**: ERPC connection → feeds data to `decoder/`
-   **`decoder/`**: Shred processing → feeds processed data to `grpc/`
-   **`grpc/`**: Server implementation → serves clients
-   **`types/`**: Shared types used across all modules
-   **`utils/`**: Helper functions used across all modules

## 🔄 Data Flow

1. **Connection Establishment**: Connect to ERPC's Direct Shreds service using `solana-stream-sdk`
2. **Subscription Management**: When clients request specific account addresses, subscribe to relevant shreds
3. **Shred Decoding**: Decode incoming shreds into `VersionedTransaction` objects using Solana's official libraries
4. **Transaction Broadcasting**: Stream filtered transactions to subscribed clients via gRPC

## 🛠️ Prerequisites

-   **Rust**: 1.80+ (stable toolchain)
-   **Protocol Buffers**: For gRPC code generation
-   **ERPC API Access**: Valid credentials for Direct Shreds service

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/dieppk/shreds-decoder.git
cd shreds-decoder

# Build the project
cargo build --release

# Run the application
cargo run --release
```

### Configuration

Create a `config.toml` file in the project root:

```toml
[server]
grpc_host = "0.0.0.0"
grpc_port = 50051

[erpc]
endpoint = "https://api.erpc.global"

```

### Environment Variables

```bash
export ERPC_ENDPOINT="https://api.erpc.global"
export GRPC_HOST="0.0.0.0"
export GRPC_PORT="50051"
```

## 🔌 API Reference

### gRPC Service Definition

```protobuf
service ShreddedTransactionService {
  // Subscribe to transactions for specific wallet addresses
  rpc SubscribeTransactions(SubscribeRequest) returns (stream TransactionResponse);

  // Health check endpoint
  rpc Ping(PingRequest) returns (PingResponse);
}

message SubscribeRequest {
  repeated string accounts = 1;
}

message TransactionResponse {
  ...
}
```

### Usage Example

```rust
// Client example
use tonic::Request;
use shreds_decoder_proto::shredded_transaction_service_client::ShreddedTransactionServiceClient;
use shreds_decoder_proto::{SubscribeRequest};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let mut client = ShreddedTransactionServiceClient::connect("http://127.0.0.1:50051").await?;

    let request = Request::new(SubscribeRequest {
        accounts: vec![
            "********************************".to_string(),
            "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA".to_string(),
        ],
    });

    let mut stream = client.subscribe_transactions(request).await?.into_inner();

    while let Some(response) = stream.message().await? {
        println!("Received transaction: {}", response.signature);
    }

    Ok(())
}
```

## 📈 Performance Optimization

-   **Connection Pooling**: Reuse connections to ERPC service
-   **Async Processing**: Full async/await implementation for non-blocking operations
-   **Memory Management**: Efficient buffer management for high-throughput scenarios
-   **Filtering**: Early filtering to reduce unnecessary processing

### Code Standards

-   Follow Rust 2024 edition idioms
-   Use `rustfmt` for code formatting
-   Pass all `clippy` lints
-   Include comprehensive documentation

## 🙏 Acknowledgments

-   [ERPC (Validators DAO)](https://erpc.global) for Direct Shreds service
-   [Solana Labs](https://solana.com) for blockchain infrastructure
-   [Anza](https://github.com/anza-xyz/agave) for Agave client libraries
