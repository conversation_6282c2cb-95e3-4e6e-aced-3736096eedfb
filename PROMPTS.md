dựa vào các quy tắc và các file tài liệu có trong dự án này, bộ quy tắc trong `.github/copilot-instructions.md`, tài liệu về dự án tại `README.md` và `README_2.md`, c<PERSON>i tài liệu khác trong thư mục `docs`, cấu trúc thư mục hiện tại, các file hiện tại đang có trong dự án, các thư viện đang được cài đặt, gi<PERSON>p tôi implement các phần cơ bản và core của dự án này trước như config manager, error handling và logging

---

Dựa vào các tài liệu và quy tắc sau đây trong dự án:

1. Bộ quy tắc coding trong `.github/copilot-instructions.md`
2. Tài liệu dự án tại `README.md` và `README_2.md`
3. <PERSON><PERSON><PERSON> tài liệu khác trong thư mục `docs/`
4. <PERSON><PERSON><PERSON> trúc thư mục hiện tại của dự án
5. Các file source code đang có
6. Các thư viện/dependencies đã được cài đặt

Hãy giúp tôi implement các module core và cơ bản sau đây theo đúng architecture và coding standards của dự án:

**Module cần implement:**

1. **Config Manager**: Quản lý cấu hình ứng dụng (environment variables, settings, configuration files)
2. **Error Handling**: Hệ thống xử lý lỗi tập trung (custom exceptions, error codes, error responses)
3. **Logging**: Hệ thống logging (log levels, formatters, handlers, structured logging)

**Yêu cầu cụ thể:**

-   Tuân thủ coding standards và best practices đã định nghĩa trong dự án
-   Sử dụng các thư viện/frameworks đã có sẵn trong dự án
-   Tạo cấu trúc file/folder phù hợp với architecture hiện tại
-   Implement theo pattern và design principles được sử dụng trong dự án
-   Đảm bảo các module có thể tích hợp dễ dàng với nhau và với các phần khác của ứng dụng
-   Viết code production-ready với error handling và validation đầy đủ

Trước khi bắt đầu implement, hãy:

1. Phân tích cấu trúc dự án hiện tại
2. Đọc và hiểu các quy tắc coding
3. Xác định technology stack và patterns đang được sử dụng
4. Đưa ra plan chi tiết cho từng module
5. Xác nhận approach với tôi trước khi bắt đầu code

---

Tham khảo file CONTEXT.md để hiểu toàn bộ context
Review key files: src/client/erpc.rs, src/config/erpc.rs, config.toml
Bắt đầu từ Next Steps: gRPC Protocol Buffers definition và server implementation
