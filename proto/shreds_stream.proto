syntax = "proto3";

package shreds_stream;

service ShredsStreamService {
    rpc SubscribeAccounts(AccountSubscriptionRequest) returns (stream VersionedTransactionResponse);
    rpc UpdateSubscription(UpdateSubscriptionRequest) returns (UpdateSubscriptionResponse);
    rpc GetActiveSubscriptions(GetActiveSubscriptionsRequest) returns (GetActiveSubscriptionsResponse);
}

message AccountSubscriptionRequest {
    repeated string accounts = 1;
    string client_id = 2;
}

message UpdateSubscriptionRequest {
    repeated string accounts = 1;
    string client_id = 2;
}

message UpdateSubscriptionResponse {
    bool success = 1;
    string message = 2;
    uint32 active_accounts_count = 3;
}

message GetActiveSubscriptionsRequest {
    string client_id = 1;
}

message GetActiveSubscriptionsResponse {
    repeated string accounts = 1;
    uint32 total_accounts = 2;
}

message VersionedTransactionResponse {
    bytes transaction_data = 1;
    uint64 slot = 2;
    string signature = 3;
    uint32 entry_index = 4;
    uint32 transaction_index = 5;
    repeated string account_keys = 6;
    uint32 instructions_count = 7;
}
