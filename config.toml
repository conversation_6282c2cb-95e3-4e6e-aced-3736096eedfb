[erpc]
endpoint = "https://shreds-far-point.erpc.global"
commitment_level = "processed"                    # Options: processed, confirmed, finalized
max_accounts_per_connection = 100                 # Rate limiting per connection

[app]
environment = "development"
port = 8080

# Pre-configured accounts that can be monitored
# Only these accounts will be subscribed to ERPC and available for client subscriptions
accounts = [
    "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
    "11111111111111111111111111111112",
    "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",
    "So11111111111111111111111111111111111111112",
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
]

[grpc]
bind_address = "127.0.0.1"
port = 50051
max_accounts_per_client = 100
broadcast_buffer_size = 1000
client_buffer_size = 100

[logging]
level = "info"
format = "pretty"
