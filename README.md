Đây là 1 ứng dụng CLI đơn giản được viết bằng Rust phiên bản mới nhất. Ứng dụng này đóng vai trò là 1 proxy server trung gian, gi<PERSON><PERSON> kết nối đến dịch vụ `Direct Shreds` của `ERPC` (`Validators DAO`) ([Quick Start Guide](https://erpc.global/en/doc/shredstream/quickstart/)), lắng nghe các shreds từ dịch vụ này, sau đó giải mã chúng ra thành các giao dịch, rồi gửi về cho client thông qua giao thức GRPC.

# Trạng thái dự án

Dự án này đang trong giai đoạn đầu tiên là tìm hiểu, lên kế hoạch và phát triển cấu trúc thư mục.

**✅ Đã hoàn thành:** Thiết kế và tạo cấu trúc thư mục tối ưu
**🔄 <PERSON><PERSON> thực hiện:** Phát triển các module core
**📋 Tiếp theo:** Implementation các tính năng chính

## Cấu trúc dự án

```
shreds-decoder/
├── proto/                         # Protocol Buffers definitions
└── src/
    ├── main.rs                    # Entry point & CLI
    ├── config/                    # Configuration management (modular config files)
    ├── client/                    # ERPC client connection
    ├── grpc/                      # gRPC server implementation
    ├── types/                     # Custom types & models
    └── utils/                     # Reusable utilities
```

### Mô tả các Module

-   **`proto/`**: Định nghĩa gRPC Protocol Buffers cho service interface
-   **`config/`**: Quản lý cấu hình theo từng service (ERPC, gRPC, logging, performance)
-   **`client/`**: Kết nối và xử lý stream từ ERPC Direct Shreds service
-   **`decoder/`**: Engine giải mã shreds thành VersionedTransaction với Reed-Solomon recovery
-   **`grpc/`**: Server gRPC streaming transactions đến clients với account filtering
-   **`types/`**: Type definitions, error handling và data models
-   **`utils/`**: Utilities tái sử dụng cho metrics, retry logic, validation

# Luồng hoạt động

1. Tạo 1 kết nối đến dịch vụ `Direct Shreds` của `ERPC`, sử dụng crate `solana-stream-sdk`
2. Khi client gửi đến 1 request có kèm theo các account address, chúng ta sẽ gửi 1 `subscribe entries` request với các accounts từ phía client lên dịch vụ `Direct Shreds` của `ERPC`, nếu server đang lắng nghe các account này rồi thì bỏ qua
3. Giải mã các shreds nhận được thành các `VersionTransaction`
4. Broadcast các `VersionTransaction` này đến các client đang lắng nghe nó, filter theo account, thông qua giao thức GRPC

# Yêu cầu

-   Tuân thủ nghiêm ngặt các quy tắc của dự án này
-   Sử dụng MCP server `Github` để truy cập vào các `Github` repository nếu cần thiết
-   Tìm kiếm thêm các thông tin từ internet nếu cần thiết
-   Sử dụng Rust phiên bản ổn định mới nhất hiện tại
-   Sử dụng các thư viện mới nhất, ổn định nhất, có nhiều lượt download nhất, hỗ trợ tốt nhất, được cộng đồng tin tưởng nhất và phù hợp nhất với dự án hiện tại
-   Sử dụng các thư viện chính thức từ Solana
-   Tìm hiểu thật kỹ về tài liệu, cách sử dụng chính xác nhất cho phiên bản mới nhất, các best practices của các thư viện sẽ dùng
-   Phân chia cấu trúc thư mục thông minh, tối ưu, đúng chức năng, dễ dàng mở rộng, bảo trì và phát triển thêm tính năng sau này
-   Không đặt quá nhiều logic trong 1 class/hàm, tách chúng ra để mỗi class/hàm chỉ làm 1 nhiệm vụ
-   Tách các logic có thể tái sử dụng ra 1 thư mục `utils` riêng, thành nhiều file theo chức năng của chúng
-   Logging đầy đủ, rõ ràng, dễ đọc, dễ debug

# Thư viện

Dưới đây là danh sách các thư viện cần thiết cho các phần quan trọng trong dự án này, trong quá trình phát triển, chúng ta có thể cài thêm 1 số thư viện khác phù hợp với dự án cho các tính năng khác.

1. `Direct Shreds` của `ERPC`

-   Dùng crate `solana-stream-sdk` được cung cấp bởi chính `ERPC` để kết nối đến dịch vụ này và lắng nghe shreds
-   Repository: `https://github.com/ValidatorsDAO/solana-stream`
-   Tài liệu tổng hợp của thư viện này nằm ở: `docs/solana-stream-sdk.md`
-   Source code của toàn bộ repository của thư viện này đã được pack lại bằng `Repomix` và nằm ở: `docs/repomix-output-ValidatorsDAO-solana-stream.xml`
-   Example: `https://raw.githubusercontent.com/ValidatorsDAO/solana-stream/refs/heads/main/client/shreds-rs/src/main.rs`

2. Giải mã shreds

-   Sử dụng luôn crate `solana_entry` từ chính Solana để giải mã
-   Link: `https://docs.rs/solana-entry/latest/solana_entry/`
-   Repository: `https://github.com/anza-xyz/agave`
-   Các thư mục quan trọng trong repository này:
    -   `https://github.com/anza-xyz/agave/tree/master/entry`
    -   `https://github.com/anza-xyz/agave/tree/master/docs/src/proposals`
-   1 vài file quan trọng:
    -   `https://raw.githubusercontent.com/anza-xyz/agave/refs/heads/master/docs/src/proposals/versioned-transactions.md`
    -   `https://raw.githubusercontent.com/anza-xyz/agave/refs/heads/master/entry/src/entry.rs`
-   Tìm thêm các thông tin khác từ Github Repository hoặc tài liệu của Solana về Agave client

3. `anyhow` cho error handling
4. `config` để đọc config từ env và file
5. `tracing` và `tracing-subscriber` cho logging

# Shreds listening

-   Phải có cơ chế tự động reconnect
-   Chỉ lắng nghe từ các accounts cần thiết

# Broadcast transaction

-   Chúng ta sẽ broadcast transactions đến cho user thông qua giao thức GRPC
-   Chúng ta sẽ có 1 method `subscribeTransactions`, với request params là 1 mảng các địa chỉ ví Solana, và method này sẽ là 1 `Readable Stream`, kiểu dữ liệu là `VersionedTransaction`
-   Có 1 method để `ping` đến server
-   Vì là 1 ứng dụng đơn giản, nên sẽ không cần authenticate
-   Có file proto để client có thể dễ dàng implement
