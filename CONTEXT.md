# SHREDS-<PERSON>CO<PERSON>R PROJECT CONTEXT

## 📋 PROJECT OVERVIEW

**Project Name:** shreds-decoder
**Language:** Rust 2024 Edition
**Purpose:** Proxy server cho Solana streaming với ERPC Direct Shreds → gRPC clients
**Target:** Sub-millisecond latency

## 🎯 CORE ARCHITECTURE DECISIONS

### Key Design Principles

1. **NO HIGH-LEVEL DECODER** - Broadcast VersionedTransaction trực tiếp đến gRPC clients
2. **Dynamic Account Subscription** - Clients gửi account lists via gRPC, không hardcode
3. **Connection Reuse** - Single ERPC connection phục vụ multiple client subscriptions
4. **Rate Limiting** - Max accounts per connection để prevent abuse

### Data Flow

```
ERPC Direct Shreds → ERPC Client → VersionedTransaction → gRPC Server → Clients
                                      ↑
                              Dynamic Account Subscription
                              (từ gRPC clients)
```

## 🏗️ IMPLEMENTED MODULES

### ✅ 1. Configuration Management (`src/config/`)

-   **Modular config system** với validation
-   **Environment variable support** (prefix: `SHREDS_`)
-   **File-based configuration** (TOML format)
-   **Production-ready defaults**

**Files:**

-   `src/config/mod.rs` - Main config loader
-   `src/config/app.rs` - Application settings
-   `src/config/erpc.rs` - ERPC client configuration
-   `src/config/grpc.rs` - gRPC server configuration
-   `src/config/logging.rs` - Logging configuration
-   `src/config/performance.rs` - Performance tuning

**Key Features:**

-   Environment variables: `SHREDS__ERPC__ENDPOINT`, `SHREDS__LOGGING__LEVEL`, etc.
-   Validation cho tất cả config values
-   Default values cho production deployment

### ✅ 2. Error Handling System (`src/types/`)

-   **Custom error types** với `thiserror`
-   **Type-safe error handling** cho từng module
-   **Structured error messages** với context

**Files:**

-   `src/types/error.rs` - Error definitions
-   `src/types/result.rs` - Result type aliases
-   `src/types/mod.rs` - Type exports

**Error Types:**

-   `ShredsError` - Top-level application errors
-   `ConfigError` - Configuration validation errors
-   `ErpcError` - ERPC client errors (ConnectionFailed, SubscriptionFailed, etc.)
-   `GrpcError` - gRPC server errors

### ✅ 3. Logging System (`src/utils/`)

-   **Structured logging** với `tracing`
-   **Multiple output formats** (JSON, Pretty, Compact)
-   **Configurable log levels**
-   **Performance metrics logging**

**Files:**

-   `src/utils/logging.rs` - Logging utilities
-   `src/utils/mod.rs` - Utility exports

### ✅ 4. ERPC Client (`src/client/`)

-   **Connection management** với retry logic
-   **Auto-reconnection** capability
-   **Dynamic account subscription** support
-   **Rate limiting** per connection
-   **VersionedTransaction processing**

**Files:**

-   `src/client/erpc.rs` - ERPC client implementation
-   `src/client/mod.rs` - Client exports

**Key Features:**

-   `ErpcClient::update_subscriptions(accounts)` - Dynamic account management
-   `ErpcClient::start_streaming()` - Continuous streaming với auto-restart
-   Connection reuse cho multiple clients
-   Rate limiting: `max_accounts_per_connection`
-   Exponential backoff retry logic

## ⚙️ CONFIGURATION

### Current Config (`config.toml`)

```toml
[erpc]
endpoint = "https://shreds-ams.erpc.global"
commitment_level = "processed"              # Options: processed, confirmed, finalized
max_accounts_per_connection = 100           # Rate limiting per connection

[app]
environment = "development"
port = 8080

[grpc]
port = 50051

[logging]
level = "info"
format = "pretty"
```

### Environment Variables

```bash
# ERPC Configuration
SHREDS__ERPC__ENDPOINT=https://shreds-ams.erpc.global
SHREDS__ERPC__COMMITMENT_LEVEL=processed
SHREDS__ERPC__MAX_ACCOUNTS_PER_CONNECTION=100

# Logging Configuration
SHREDS__LOGGING__LEVEL=info
SHREDS__LOGGING__FORMAT=json

# App Configuration
SHREDS__APP__ENVIRONMENT=production
```

## 🔧 DEPENDENCIES

### Core Dependencies (Cargo.toml)

```toml
[dependencies]
anyhow = "1.0"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["json", "env-filter"] }
tokio = { version = "1.0", features = ["full"] }
tokio-stream = "0.1"
serde = { version = "1.0", features = ["derive"] }
config = "0.14"
tonic = "0.12"

# Solana Dependencies
solana-stream-sdk = "0.2.5"
solana-entry = "2.2.1"
solana-transaction = "2.2.2"
bincode = "1.3.3"
```

## 📁 PROJECT STRUCTURE

```
shreds-decoder/
├── src/
│   ├── main.rs                 # Application entry point
│   ├── client/                 # ERPC client module
│   │   ├── mod.rs
│   │   └── erpc.rs            # ERPC client implementation
│   ├── config/                 # Configuration management
│   │   ├── mod.rs
│   │   ├── app.rs
│   │   ├── erpc.rs
│   │   ├── grpc.rs
│   │   ├── logging.rs
│   │   └── performance.rs
│   ├── types/                  # Type definitions
│   │   ├── mod.rs
│   │   ├── error.rs
│   │   └── result.rs
│   └── utils/                  # Utilities
│       ├── mod.rs
│       └── logging.rs
├── config.toml                 # Configuration file
├── Cargo.toml                  # Dependencies
├── IMPLEMENTATION.md           # Implementation status
└── CONTEXT.md                  # This file
```

## 🚀 CURRENT STATUS

### ✅ COMPLETED FEATURES

1. **Core Infrastructure**

    - ✅ Error handling system
    - ✅ Configuration management
    - ✅ Structured logging
    - ✅ Project architecture

2. **ERPC Client**

    - ✅ Connection với retry logic
    - ✅ Dynamic account subscription
    - ✅ Rate limiting per connection
    - ✅ VersionedTransaction processing
    - ✅ Auto-reconnection capability
    - ✅ Configurable commitment level

3. **Configuration Features**
    - ✅ Commitment level configurable (processed/confirmed/finalized)
    - ✅ Rate limiting configurable (max_accounts_per_connection)
    - ✅ Environment variable support
    - ✅ Production-ready defaults

### 🔄 CURRENT FUNCTIONALITY

**Application hiện tại có thể:**

1. Load configuration từ file/environment variables
2. Connect đến ERPC endpoint với retry logic
3. Subscribe đến dynamic account lists
4. Process raw shreds → VersionedTransaction
5. Log detailed transaction information
6. Auto-reconnect khi connection bị mất
7. Rate limiting cho account subscriptions

### 📊 SAMPLE OUTPUT

```
INFO shreds_proxy: Application starting, app_name: shreds-proxy
INFO shreds_proxy: Using test accounts for monitoring
INFO shreds_proxy::client::erpc: Updating account subscriptions, old_count: 0, new_count: 5
INFO shreds_proxy::client::erpc: Starting stream for subscribed accounts
INFO shreds_proxy::client::erpc: Connected to ERPC successfully
INFO shreds_proxy::client::erpc: Successfully subscribed to ERPC stream
INFO shreds_proxy::client::erpc: Received slot entry, slot: *********, entries_count: 5
INFO shreds_proxy::client::erpc: Raw transaction received, account_keys_count: 3
```

## 🎯 NEXT STEPS

### 🚧 PENDING IMPLEMENTATION

1. **gRPC Server Module** (`src/grpc/`)

    - Protocol Buffers definitions
    - Service implementation cho account subscription
    - VersionedTransaction streaming
    - Client connection management

2. **Integration**

    - Connect ERPC client với gRPC server
    - Dynamic account subscription từ clients
    - Broadcast VersionedTransaction đến multiple clients

3. **Testing & Optimization**
    - Unit tests cho core modules
    - Integration tests
    - Performance optimization
    - Memory management

### 📋 IMMEDIATE NEXT TASKS

1. **Define Protocol Buffers** - Service interface cho gRPC
2. **Implement gRPC Server** - Account subscription service
3. **Connect Components** - ERPC client → gRPC server integration
4. **Testing** - End-to-end functionality testing

## 🔍 TECHNICAL DETAILS

### Key Methods (ERPC Client)

```rust
// Dynamic account management
pub async fn update_subscriptions(&mut self, new_accounts: Vec<String>) -> ErpcResult<()>

// Get current subscriptions
pub async fn get_subscribed_accounts(&self) -> Vec<String>

// Start continuous streaming
pub async fn start_streaming(&mut self) -> ErpcResult<()>

// Initialize with test accounts
pub async fn run_with_initial_accounts(&mut self, accounts: Vec<String>) -> ErpcResult<()>
```

### Configuration Types

```rust
pub struct ErpcConfig {
    pub endpoint: String,
    pub commitment_level: CommitmentLevel,
    pub max_accounts_per_connection: usize,
    // ... other fields
}

pub enum CommitmentLevel {
    Processed,
    Confirmed,
    Finalized,
}
```

## 📝 CODING STANDARDS

-   **No comments in source code** by default
-   **App name from Cargo.toml**
-   **bind_address default 127.0.0.1**
-   **ERPC endpoint required with no default**
-   **English for all strings in source code**
-   **Vietnamese for documentation**

## 🎯 PROJECT GOALS

-   **Sub-millisecond latency** streaming
-   **Production-ready** reliability
-   **Scalable** architecture
-   **Dynamic** account management
-   **Rate limiting** protection
-   **Clean** codebase following best practices

## 🧠 CONVERSATION HISTORY & DECISIONS

### Key Clarifications Made

1. **Commitment Level Configurable** - User requested configurable commitment level
2. **No High-Level Decoder Needed** - User clarified to broadcast VersionedTransaction directly
3. **Dynamic Account Subscription** - User emphasized accounts come from clients, not hardcoded
4. **Connection Reuse Required** - Single ERPC connection must serve multiple clients
5. **Rate Limiting Essential** - max_accounts_per_connection to prevent abuse

### Architecture Evolution

-   **Initial:** ERPC → Decoder → gRPC → Clients
-   **Final:** ERPC → VersionedTransaction → gRPC → Clients (no decoder layer)

### Implementation Decisions

-   Used `Arc<RwLock<HashSet<String>>>` for thread-safe account management
-   Implemented exponential backoff for connection retries
-   Added comprehensive error handling with context
-   Structured logging for production debugging
-   Environment variable support for deployment flexibility

## 🔧 BUILD & RUN COMMANDS

### Development

```bash
# Build project
cargo build

# Run with default config
cargo run

# Run with custom config
CONFIG_FILE=custom-config.toml cargo run

# Check for errors
cargo check

# Run tests (when implemented)
cargo test
```

### Production Deployment

```bash
# Build optimized binary
cargo build --release

# Run with environment variables
SHREDS__ERPC__ENDPOINT=https://prod-endpoint.com \
SHREDS__LOGGING__LEVEL=info \
SHREDS__LOGGING__FORMAT=json \
./target/release/shreds-proxy
```

## 🐛 TROUBLESHOOTING

### Common Issues

1. **Connection Failed** - Check ERPC endpoint availability
2. **Transport Error** - Verify network connectivity and firewall
3. **Too Many Accounts** - Reduce accounts or increase max_accounts_per_connection
4. **Config Missing** - Ensure ERPC endpoint is configured

### Debug Commands

```bash
# Verbose logging
SHREDS__LOGGING__LEVEL=debug cargo run

# JSON logging for parsing
SHREDS__LOGGING__FORMAT=json cargo run

# Check configuration loading
RUST_LOG=shreds_proxy::config=debug cargo run
```

## 📚 MEMORY CONTEXT

### User Preferences (from memories)

-   Implements core modules following project's coding standards in .github/copilot-instructions.md
-   Uses existing libraries/frameworks and maintains consistency with current architecture patterns
-   Wants commitment level configurable in ERPC config
-   Needs clarification on current decoder capabilities and connection reuse behavior
-   Prefers Vietnamese responses and English source code
-   Focuses on clean code, best practices, and production-ready solutions

### Project Specifics (from memories)

-   Dự án shreds-decoder sử dụng Rust 2024
-   Architecture: proxy server cho Solana streaming với ERPC Direct Shreds → gRPC clients
-   Target: sub-millisecond latency
-   Error Handling: sử dụng anyhow/thiserror
-   Logging: sử dụng tracing
-   Async Runtime: tokio
-   Broadcast VersionedTransaction trực tiếp đến gRPC clients
-   Dynamic account subscription từ clients với connection reuse và rate limiting max_accounts_per_connection
-   Không cần high-level decoder

---

**Last Updated:** 2025-06-02
**Status:** ERPC Client implemented, ready for gRPC Server implementation
**Next Session:** Start with gRPC Protocol Buffers definition and server implementation
**Key Files to Review:** `src/client/erpc.rs`, `src/config/erpc.rs`, `config.toml`
