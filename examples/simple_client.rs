use tonic::Request;

// Import generated gRPC types
mod proto {
    tonic::include_proto!("shreds_stream");
}

use proto::shreds_stream_client::ShredsStreamClient;
use proto::{AccountSubscriptionRequest, GetActiveSubscriptionsRequest, UpdateSubscriptionRequest};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 Testing gRPC Client Connection...");

    // Connect to gRPC server
    let mut client = ShredsStreamClient::connect("http://127.0.0.1:50051").await?;
    println!("✅ Connected to gRPC server successfully!");

    // Test 1: Subscribe to accounts
    println!("\n📋 Test 1: Subscribe to valid account...");
    let request = Request::new(AccountSubscriptionRequest {
        accounts: vec!["6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P".to_string()],
        client_id: "test-client-1".to_string(),
    });

    match client.subscribe_accounts(request).await {
        Ok(response) => {
            println!("✅ Subscription successful!");
            let mut stream = response.into_inner();

            // Read a few transactions
            for i in 0..3 {
                match stream.message().await {
                    Ok(Some(transaction)) => {
                        println!(
                            "📦 Transaction {}: slot={}, entry_idx={}, tx_idx={}",
                            i + 1,
                            transaction.slot,
                            transaction.entry_idx,
                            transaction.tx_idx
                        );
                    }
                    Ok(None) => {
                        println!("🔚 Stream ended");
                        break;
                    }
                    Err(e) => {
                        println!("❌ Stream error: {}", e);
                        break;
                    }
                }
            }
        }
        Err(e) => {
            println!("❌ Subscription failed: {}", e);
        }
    }

    // Test 2: Try invalid account
    println!("\n📋 Test 2: Try invalid account (should fail)...");
    let request = Request::new(AccountSubscriptionRequest {
        accounts: vec!["InvalidAccount123".to_string()],
        client_id: "test-client-2".to_string(),
    });

    match client.subscribe_accounts(request).await {
        Ok(_) => {
            println!("❌ This should have failed!");
        }
        Err(e) => {
            println!("✅ Correctly rejected invalid account: {}", e);
        }
    }

    // Test 3: Try account not in allowed list
    println!("\n📋 Test 3: Try account not in allowed list (should fail)...");
    let request = Request::new(AccountSubscriptionRequest {
        accounts: vec!["9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM".to_string()],
        client_id: "test-client-3".to_string(),
    });

    match client.subscribe_accounts(request).await {
        Ok(_) => {
            println!("❌ This should have failed!");
        }
        Err(e) => {
            println!("✅ Correctly rejected unauthorized account: {}", e);
        }
    }

    println!("\n🎉 All tests completed!");
    Ok(())
}
