use tokio::sync::broadcast;
use tracing::{error, info, warn};

use solana_transaction::versioned::VersionedTransaction;

use super::proto::VersionedTransactionResponse;
use crate::types::{GrpcError, GrpcResult};

pub struct TransactionBroadcaster {
    sender: broadcast::Sender<VersionedTransactionResponse>,
}

impl TransactionBroadcaster {
    pub fn new(sender: broadcast::Sender<VersionedTransactionResponse>) -> Self {
        Self { sender }
    }

    pub async fn broadcast_transaction(
        &self,
        transaction: &VersionedTransaction,
        slot: u64,
        entry_index: u32,
        transaction_index: u32,
    ) -> GrpcResult<()> {
        let signature = if !transaction.signatures.is_empty() {
            transaction.signatures[0].to_string()
        } else {
            "no_signature".to_string()
        };

        let account_keys: Vec<String> = transaction
            .message
            .static_account_keys()
            .iter()
            .map(|key| key.to_string())
            .collect();

        let instructions_count = transaction.message.instructions().len() as u32;

        let transaction_data = match bincode::serialize(transaction) {
            Ok(data) => data,
            Err(e) => {
                error!(
                    slot = slot,
                    entry_index = entry_index,
                    transaction_index = transaction_index,
                    error = %e,
                    "Failed to serialize transaction"
                );
                return Err(GrpcError::Service {
                    message: format!("Transaction serialization failed: {}", e),
                });
            }
        };

        let response = VersionedTransactionResponse {
            transaction_data,
            slot,
            signature: signature.clone(),
            entry_index,
            transaction_index,
            account_keys,
            instructions_count,
        };

        match self.sender.send(response) {
            Ok(receiver_count) => {
                info!(
                    slot = slot,
                    entry_index = entry_index,
                    transaction_index = transaction_index,
                    signature = %signature,
                    receiver_count = receiver_count,
                    "Transaction broadcasted to gRPC clients"
                );
                Ok(())
            }
            Err(e) => {
                warn!(
                    slot = slot,
                    entry_index = entry_index,
                    transaction_index = transaction_index,
                    error = %e,
                    "No active receivers for transaction broadcast"
                );
                Ok(())
            }
        }
    }

    pub fn get_receiver_count(&self) -> usize {
        self.sender.receiver_count()
    }
}
