use std::net::SocketAddr;
use tokio::sync::broadcast;
use tonic::transport::Server;
use tonic_reflection::server::Builder as ReflectionBuilder;
use tracing::{error, info};

use super::proto::{VersionedTransactionResponse, shreds_stream_service_server::ShredsStreamServiceServer};
use super::service::ShredsStreamServiceImpl;
use crate::config::GrpcConfig;
use crate::types::{GrpcError, GrpcResult};

pub struct GrpcServer {
    config: GrpcConfig,
    service: ShredsStreamServiceImpl,
}

impl GrpcServer {
    pub fn new(config: GrpcConfig, allowed_accounts: Vec<String>) -> Self {
        let service = ShredsStreamServiceImpl::new(config.clone(), allowed_accounts);

        Self { config, service }
    }

    pub fn get_service(&self) -> &ShredsStreamServiceImpl {
        &self.service
    }

    pub fn get_transaction_sender(&self) -> broadcast::Sender<VersionedTransactionResponse> {
        self.service.get_transaction_sender()
    }

    pub async fn get_all_subscribed_accounts(&self) -> Vec<String> {
        self.service.get_all_subscribed_accounts().await
    }

    pub async fn start(&self) -> GrpcResult<()> {
        let bind_address = self.config.bind_address.clone();
        let port = self.config.port;

        let addr: SocketAddr =
            format!("{}:{}", bind_address, port)
                .parse()
                .map_err(|e| GrpcError::ServerStartFailed {
                    message: format!("Invalid bind address: {}", e),
                })?;

        info!(
            bind_address = %bind_address,
            port = port,
            addr = %addr,
            "Starting gRPC server"
        );

        let service_server = ShredsStreamServiceServer::new(self.service.clone());

        let server = Server::builder().add_service(service_server);

        info!(addr = %addr, "gRPC server listening");

        if let Err(e) = server.serve(addr).await {
            error!(error = %e, "gRPC server failed");
            return Err(GrpcError::ServerStartFailed {
                message: format!("Server failed to start: {}", e),
            });
        }

        Ok(())
    }

    pub async fn start_with_shutdown(&self, shutdown_signal: impl std::future::Future<Output = ()>) -> GrpcResult<()> {
        let bind_address = self.config.bind_address.clone();
        let port = self.config.port;

        let addr: SocketAddr =
            format!("{}:{}", bind_address, port)
                .parse()
                .map_err(|e| GrpcError::ServerStartFailed {
                    message: format!("Invalid bind address: {}", e),
                })?;

        info!(
            bind_address = %bind_address,
            port = port,
            addr = %addr,
            "Starting gRPC server with graceful shutdown"
        );

        let service_server = ShredsStreamServiceServer::new(self.service.clone());

        let server = Server::builder().add_service(service_server);

        info!(addr = %addr, "gRPC server listening with shutdown signal");

        if let Err(e) = server.serve_with_shutdown(addr, shutdown_signal).await {
            error!(error = %e, "gRPC server failed");
            return Err(GrpcError::ServerStartFailed {
                message: format!("Server failed to start: {}", e),
            });
        }

        info!("gRPC server shutdown gracefully");
        Ok(())
    }
}
