use std::collections::HashMap;
use std::pin::Pin;
use std::sync::Arc;
use tokio::sync::{RwLock, broadcast};
use tokio_stream::{Stream, StreamExt, wrappers::BroadcastStream};
use tonic::{Request, Response, Status};
use tracing::{error, info, warn};

use super::proto::{
    AccountSubscriptionRequest, GetActiveSubscriptionsRequest, GetActiveSubscriptionsResponse,
    UpdateSubscriptionRequest, UpdateSubscriptionResponse, VersionedTransactionResponse,
    shreds_stream_service_server::ShredsStreamService,
};

use crate::config::GrpcConfig;
use crate::types::{GrpcError, GrpcResult};

pub type TransactionStream = Pin<Box<dyn Stream<Item = Result<VersionedTransactionResponse, Status>> + Send>>;

#[derive(Clone)]
pub struct ClientSubscription {
    pub client_id: String,
    pub accounts: Vec<String>,
    pub sender: broadcast::Sender<VersionedTransactionResponse>,
}

#[derive(Clone)]
pub struct ShredsStreamServiceImpl {
    config: GrpcConfig,
    clients: Arc<RwLock<HashMap<String, ClientSubscription>>>,
    transaction_sender: broadcast::Sender<VersionedTransactionResponse>,
    allowed_accounts: Vec<String>,
}

impl ShredsStreamServiceImpl {
    pub fn new(config: GrpcConfig, allowed_accounts: Vec<String>) -> Self {
        let (transaction_sender, _) = broadcast::channel(config.broadcast_buffer_size);

        Self {
            config,
            clients: Arc::new(RwLock::new(HashMap::new())),
            transaction_sender,
            allowed_accounts,
        }
    }

    pub fn get_transaction_sender(&self) -> broadcast::Sender<VersionedTransactionResponse> {
        self.transaction_sender.clone()
    }

    pub async fn get_all_subscribed_accounts(&self) -> Vec<String> {
        let clients = self.clients.read().await;
        let mut all_accounts = Vec::new();

        for client in clients.values() {
            all_accounts.extend(client.accounts.clone());
        }

        all_accounts.sort();
        all_accounts.dedup();
        all_accounts
    }

    async fn validate_accounts(&self, accounts: &[String]) -> GrpcResult<()> {
        if accounts.len() > self.config.max_accounts_per_client {
            return Err(GrpcError::InvalidRequest {
                message: format!(
                    "Too many accounts: {} > {}",
                    accounts.len(),
                    self.config.max_accounts_per_client
                ),
            });
        }

        for account in accounts {
            if account.is_empty() || account.len() < 32 || account.len() > 44 {
                return Err(GrpcError::InvalidRequest {
                    message: format!(
                        "Invalid account address length: {} (expected 32-44 characters)",
                        account
                    ),
                });
            }

            if !self.allowed_accounts.contains(account) {
                return Err(GrpcError::InvalidRequest {
                    message: format!(
                        "Account '{}' is not in the pre-configured account list. Allowed accounts: {:?}",
                        account, self.allowed_accounts
                    ),
                });
            }
        }

        Ok(())
    }
}

#[tonic::async_trait]
impl ShredsStreamService for ShredsStreamServiceImpl {
    type SubscribeAccountsStream = TransactionStream;

    async fn subscribe_accounts(
        &self,
        request: Request<AccountSubscriptionRequest>,
    ) -> Result<Response<Self::SubscribeAccountsStream>, Status> {
        let req = request.into_inner();
        let client_id = if req.client_id.is_empty() {
            format!("client_{}", uuid::Uuid::new_v4())
        } else {
            req.client_id
        };

        info!(
            client_id = %client_id,
            accounts_count = req.accounts.len(),
            "New account subscription request"
        );

        if let Err(e) = self.validate_accounts(&req.accounts).await {
            error!(client_id = %client_id, error = %e, "Account validation failed");
            return Err(Status::invalid_argument(e.to_string()));
        }

        let receiver = self.transaction_sender.subscribe();

        let subscription = ClientSubscription {
            client_id: client_id.clone(),
            accounts: req.accounts.clone(),
            sender: self.transaction_sender.clone(),
        };

        {
            let mut clients = self.clients.write().await;
            clients.insert(client_id.clone(), subscription);
        }

        info!(
            client_id = %client_id,
            accounts = ?req.accounts,
            "Client subscription registered"
        );

        let stream = BroadcastStream::new(receiver).map(|result| match result {
            Ok(transaction) => Ok(transaction),
            Err(e) => {
                warn!(error = %e, "Broadcast stream error");
                Err(Status::internal("Stream error"))
            }
        });

        Ok(Response::new(Box::pin(stream)))
    }

    async fn update_subscription(
        &self,
        request: Request<UpdateSubscriptionRequest>,
    ) -> Result<Response<UpdateSubscriptionResponse>, Status> {
        let req = request.into_inner();
        let client_id = req.client_id;

        info!(
            client_id = %client_id,
            new_accounts_count = req.accounts.len(),
            "Subscription update request"
        );

        if let Err(e) = self.validate_accounts(&req.accounts).await {
            error!(client_id = %client_id, error = %e, "Account validation failed");
            return Ok(Response::new(UpdateSubscriptionResponse {
                success: false,
                message: e.to_string(),
                active_accounts_count: 0,
            }));
        }

        let mut clients = self.clients.write().await;

        if let Some(subscription) = clients.get_mut(&client_id) {
            subscription.accounts = req.accounts.clone();

            info!(
                client_id = %client_id,
                accounts = ?req.accounts,
                "Subscription updated successfully"
            );

            Ok(Response::new(UpdateSubscriptionResponse {
                success: true,
                message: "Subscription updated successfully".to_string(),
                active_accounts_count: req.accounts.len() as u32,
            }))
        } else {
            warn!(client_id = %client_id, "Client not found for subscription update");
            Ok(Response::new(UpdateSubscriptionResponse {
                success: false,
                message: "Client not found".to_string(),
                active_accounts_count: 0,
            }))
        }
    }

    async fn get_active_subscriptions(
        &self,
        request: Request<GetActiveSubscriptionsRequest>,
    ) -> Result<Response<GetActiveSubscriptionsResponse>, Status> {
        let req = request.into_inner();
        let client_id = req.client_id;

        let clients = self.clients.read().await;

        if let Some(subscription) = clients.get(&client_id) {
            Ok(Response::new(GetActiveSubscriptionsResponse {
                accounts: subscription.accounts.clone(),
                total_accounts: subscription.accounts.len() as u32,
            }))
        } else {
            Ok(Response::new(GetActiveSubscriptionsResponse {
                accounts: vec![],
                total_accounts: 0,
            }))
        }
    }
}
