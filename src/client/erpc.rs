use std::time::Duration;
use tokio::time::sleep;
use tokio_stream::StreamExt;
use tracing::{debug, error, info, warn};

use solana_entry::entry::Entry as SolanaEntry;
use solana_stream_sdk::ShredstreamClient;
use solana_stream_sdk::shredstream_proto::Entry as StreamEntry;
use solana_transaction::versioned::VersionedTransaction;

use crate::config::ErpcConfig;
use crate::grpc::TransactionBroadcaster;
use crate::types::{ErpcError, ErpcResult};

pub struct ErpcClient {
    client: ShredstreamClient,
    config: ErpcConfig,
    monitored_accounts: Vec<String>,
    broadcaster: Option<TransactionBroadcaster>,
}

pub struct ErpcClientBuilder {
    config: ErpcConfig,
    broadcaster: Option<TransactionBroadcaster>,
    accounts: Vec<String>,
}

impl ErpcClientBuilder {
    pub fn new(config: ErpcConfig) -> Self {
        Self {
            config,
            broadcaster: None,
            accounts: vec![],
        }
    }

    pub fn with_accounts(mut self, accounts: Vec<String>) -> Self {
        self.accounts = accounts;
        self
    }

    pub async fn build(self) -> ErpcResult<ErpcClient> {
        let client = Self::connect_with_retry(&self.config.endpoint, &self.config).await?;

        Ok(ErpcClient {
            client,
            config: self.config,
            monitored_accounts: self.accounts,
            broadcaster: self.broadcaster,
        })
    }

    pub fn with_broadcaster(mut self, broadcaster: TransactionBroadcaster) -> Self {
        self.broadcaster = Some(broadcaster);
        self
    }

    async fn connect_with_retry(endpoint: &str, config: &ErpcConfig) -> ErpcResult<ShredstreamClient> {
        let max_retries = config.max_retries;
        for attempt in 0..max_retries {
            match ShredstreamClient::connect(endpoint).await {
                Ok(client) => {
                    info!(endpoint = %endpoint, attempt = attempt + 1, "Connected to ERPC successfully");
                    return Ok(client);
                }
                Err(e) if attempt == max_retries - 1 => {
                    error!(endpoint = %endpoint, error = %e, "Failed to connect to ERPC after all retries");
                    return Err(ErpcError::ConnectionFailed {
                        message: format!("Failed to connect after {} attempts: {}", max_retries, e),
                    });
                }
                Err(e) => {
                    warn!(
                        endpoint = %endpoint,
                        attempt = attempt + 1,
                        max_retries = max_retries,
                        error = %e,
                        "Connection attempt failed, retrying..."
                    );
                    let delay_ms = (config.initial_retry_delay_ms as f64
                        * config.retry_backoff_multiplier.powi(attempt as i32))
                        as u64;
                    let delay = Duration::from_millis(delay_ms.min(config.max_retry_delay_ms));
                    sleep(delay).await;
                }
            }
        }
        unreachable!()
    }
}

impl ErpcClient {
    pub fn builder(config: ErpcConfig) -> ErpcClientBuilder {
        ErpcClientBuilder::new(config)
    }

    pub fn get_monitored_accounts(&self) -> &[String] {
        &self.monitored_accounts
    }

    pub fn set_broadcaster(&mut self, broadcaster: TransactionBroadcaster) {
        self.broadcaster = Some(broadcaster);
    }

    pub async fn start_streaming(&mut self) -> ErpcResult<()> {
        if self.monitored_accounts.is_empty() {
            return Err(ErpcError::InvalidRequest {
                message: "No accounts configured for monitoring".to_string(),
            });
        }

        info!(
            accounts = ?self.monitored_accounts,
            accounts_count = self.monitored_accounts.len(),
            "Starting stream for pre-configured accounts"
        );

        loop {
            let commitment_level = self.config.commitment_level.clone().into();
            let request = ShredstreamClient::create_entries_request_for_accounts(
                self.monitored_accounts.clone(),
                vec![],
                vec![],
                Some(commitment_level),
            );

            let mut stream =
                self.client
                    .subscribe_entries(request)
                    .await
                    .map_err(|e| ErpcError::SubscriptionFailed {
                        message: format!("Failed to subscribe to entries: {}", e),
                    })?;

            info!("Successfully subscribed to ERPC stream, starting to process entries...");

            while let Some(entry_result) = stream.next().await {
                match entry_result {
                    Ok(slot_entry) => {
                        if let Err(e) = self.process_slot_entry(&slot_entry).await {
                            error!(error = %e, "Failed to process slot entry");
                        }
                    }
                    Err(e) => {
                        error!(error = %e, "Stream error occurred");
                        warn!("Stream error, attempting to reconnect...");
                        return Err(ErpcError::StreamDisconnected {
                            message: format!("Stream disconnected: {}", e),
                        });
                    }
                }
            }

            warn!("ERPC stream ended unexpectedly, will restart...");

            if !self.config.auto_reconnect {
                break;
            }

            let delay = Duration::from_millis(self.config.initial_retry_delay_ms);
            info!(delay_ms = delay.as_millis(), "Waiting before restart");
            tokio::time::sleep(delay).await;
        }

        Ok(())
    }

    async fn process_slot_entry(&self, slot_entry: &StreamEntry) -> ErpcResult<()> {
        let entries = match bincode::deserialize::<Vec<SolanaEntry>>(&slot_entry.entries) {
            Ok(entries) => entries,
            Err(e) => {
                error!(
                    slot = slot_entry.slot,
                    error = %e,
                    "Failed to deserialize entries"
                );
                return Err(ErpcError::InvalidResponse {
                    message: format!("Deserialization failed: {}", e),
                });
            }
        };

        let total_transactions: usize = entries.iter().map(|e| e.transactions.len()).sum();

        debug!(
            slot = slot_entry.slot,
            entries_count = entries.len(),
            transactions_count = total_transactions,
            "Received slot entry"
        );

        for (entry_idx, entry) in entries.iter().enumerate() {
            self.process_entry(slot_entry.slot, entry_idx, entry).await?;
        }

        Ok(())
    }

    async fn process_entry(&self, slot: u64, entry_idx: usize, entry: &SolanaEntry) -> ErpcResult<()> {
        debug!(
            slot = slot,
            entry_idx = entry_idx,
            num_hashes = entry.num_hashes,
            hash = %entry.hash,
            transactions_count = entry.transactions.len(),
            "Processing entry"
        );

        for (tx_idx, transaction) in entry.transactions.iter().enumerate() {
            self.log_transaction_info(slot, entry_idx, tx_idx, transaction);

            if let Some(ref broadcaster) = self.broadcaster {
                if let Err(e) = broadcaster
                    .broadcast_transaction(transaction, slot, entry_idx as u32, tx_idx as u32)
                    .await
                {
                    error!(
                        slot = slot,
                        entry_idx = entry_idx,
                        tx_idx = tx_idx,
                        error = %e,
                        "Failed to broadcast transaction to gRPC clients"
                    );
                }
            }
        }

        Ok(())
    }

    fn log_transaction_info(&self, slot: u64, entry_idx: usize, tx_idx: usize, transaction: &VersionedTransaction) {
        let message = &transaction.message;
        let account_keys_len = message.static_account_keys().len();
        let instructions_len = message.instructions().len();

        debug!(
            slot = slot,
            entry_idx = entry_idx,
            tx_idx = tx_idx,
            account_keys_count = account_keys_len,
            instructions_count = instructions_len,
            signatures_count = transaction.signatures.len(),
            "Raw transaction received"
        );

        for (sig_idx, signature) in transaction.signatures.iter().enumerate() {
            debug!(
                slot = slot,
                entry_idx = entry_idx,
                tx_idx = tx_idx,
                sig_idx = sig_idx,
                signature = %signature,
                "Transaction signature"
            );
        }
    }

    pub async fn start_monitoring(&mut self) -> ErpcResult<()> {
        self.start_streaming().await
    }
}
