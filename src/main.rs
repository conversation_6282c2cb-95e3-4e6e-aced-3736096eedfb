use anyhow::Result;

mod client;
mod config;
mod grpc;
mod types;
mod utils;

use client::ErpcClient;
use config::MainConfig;
use grpc::{GrpcServer, TransactionBroadcaster};
use utils::{init_logging, log_startup_info};

#[tokio::main]
async fn main() -> Result<()> {
    let config = MainConfig::load().map_err(|e| anyhow::anyhow!("Failed to load config: {}", e))?;

    init_logging(&config.logging).map_err(|e| anyhow::anyhow!("Failed to initialize logging: {}", e))?;

    log_startup_info(&config);

    tracing::info!("Configuration loaded successfully!");
    tracing::info!(app_name = %config.app.name, "Application initialized");

    let accounts = config.app.get_accounts().to_vec();
    if accounts.is_empty() {
        return Err(anyhow::anyhow!(
            "No accounts configured for monitoring. Please add accounts to config.toml"
        ));
    }

    tracing::info!(
        accounts = ?accounts,
        accounts_count = accounts.len(),
        "Loaded pre-configured accounts for monitoring"
    );

    tracing::info!("Starting gRPC server...");
    let grpc_server = GrpcServer::new(config.grpc.clone(), accounts.clone());
    let transaction_sender = grpc_server.get_transaction_sender();
    let broadcaster = TransactionBroadcaster::new(transaction_sender);

    let mut erpc_client = ErpcClient::builder(config.erpc.clone())
        .with_accounts(accounts)
        .with_broadcaster(broadcaster)
        .build()
        .await
        .map_err(|e| anyhow::anyhow!("Failed to create ERPC client: {}", e))?;

    tracing::info!("ERPC client created successfully");

    let grpc_handle = tokio::spawn(async move {
        if let Err(e) = grpc_server.start().await {
            tracing::error!(error = %e, "gRPC server failed");
        }
    });

    let erpc_handle = tokio::spawn(async move {
        if let Err(e) = erpc_client.start_monitoring().await {
            tracing::error!(error = %e, "ERPC client failed");
        }
    });

    tracing::info!("Both gRPC server and ERPC client started, waiting for completion...");

    tokio::select! {
        _ = grpc_handle => {
            tracing::info!("gRPC server completed");
        }
        _ = erpc_handle => {
            tracing::info!("ERPC client completed");
        }
    }

    Ok(())
}
