use serde::{Deserialize, Serialize};

use crate::types::{ConfigError, ConfigResult};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub name: String,
    pub version: String,
    pub environment: Environment,
    pub bind_address: String,
    pub port: u16,

    pub accounts: Vec<String>,
    pub max_connections: usize,
    pub request_timeout_ms: u64,
    pub enable_metrics: bool,
    pub metrics_port: Option<u16>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum Environment {
    Development,
    Staging,
    Production,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            name: env!("CARGO_PKG_NAME").to_string(),
            version: env!("CARGO_PKG_VERSION").to_string(),
            environment: Environment::Development,
            bind_address: "127.0.0.1".to_string(),
            port: 8080,
            accounts: vec![],
            max_connections: 1000,
            request_timeout_ms: 5000,
            enable_metrics: false,
            metrics_port: None,
        }
    }
}

impl AppConfig {
    pub fn validate(&self) -> ConfigResult<()> {
        if self.name.is_empty() {
            return Err(ConfigError::InvalidValue {
                key: "name".to_string(),
                value: self.name.clone(),
            });
        }

        if self.port == 0 {
            return Err(ConfigError::InvalidValue {
                key: "port".to_string(),
                value: self.port.to_string(),
            });
        }

        if self.max_connections == 0 {
            return Err(ConfigError::InvalidValue {
                key: "max_connections".to_string(),
                value: self.max_connections.to_string(),
            });
        }

        if self.request_timeout_ms == 0 {
            return Err(ConfigError::InvalidValue {
                key: "request_timeout_ms".to_string(),
                value: self.request_timeout_ms.to_string(),
            });
        }

        if self.enable_metrics && self.metrics_port.is_none() {
            return Err(ConfigError::MissingRequired {
                key: "metrics_port".to_string(),
            });
        }

        Ok(())
    }

    pub fn is_production(&self) -> bool {
        matches!(self.environment, Environment::Production)
    }

    pub fn is_development(&self) -> bool {
        matches!(self.environment, Environment::Development)
    }

    pub fn get_accounts(&self) -> &[String] {
        &self.accounts
    }

    pub fn is_account_allowed(&self, account: &str) -> bool {
        self.accounts.contains(&account.to_string())
    }

    pub fn validate_accounts(&self, requested_accounts: &[String]) -> Result<(), String> {
        for account in requested_accounts {
            if !self.is_account_allowed(account) {
                return Err(format!(
                    "Account '{}' is not in the pre-configured account list. Allowed accounts: {:?}",
                    account, self.accounts
                ));
            }
        }
        Ok(())
    }
}
